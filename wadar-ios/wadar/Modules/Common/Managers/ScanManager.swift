/**
 * 扫码管理器
 * 负责管理扫码功能的核心逻辑
 * 
 * <AUTHOR>
 */

import Foundation
import AVFoundation
import Combine
import UIKit

/**
 * 扫码管理器类
 * 实现扫码服务协议，提供完整的扫码功能
 */
class ScanManager: NSObject, ScanServiceProtocol {
    
    // MARK: - 属性
    
    /// 单例实例
    static let shared = ScanManager()
    
    /// 相机会话
    private var captureSession: AVCaptureSession?
    
    /// 视频预览层
    private var previewLayer: AVCaptureVideoPreviewLayer?
    
    /// 扫码结果Subject
    private let scanResultSubject = PassthroughSubject<ScanResult, Never>()
    
    /// 权限状态Subject
    private let permissionSubject = PassthroughSubject<Bool, Never>()
    
    /// 当前扫码配置
    private var currentConfig: ScanConfig?
    
    /// 扫码超时定时器
    private var timeoutTimer: Timer?
    
    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    /// 委托对象
    weak var delegate: ScanServiceDelegate?
    
    // MARK: - ScanServiceProtocol 实现
    
    var isAvailable: Bool {
        return UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    private(set) var isScanning: Bool = false
    
    private override init() {
        super.init()
        setupNotifications()
    }
    
    deinit {
        stopScan()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 公共方法
    
    func startScan(with config: ScanConfig) -> AnyPublisher<ScanResult, Never> {
        print("[ScanManager] 开始扫码，配置: \(config)")
        
        currentConfig = config
        
        // 检查设备可用性
        guard isAvailable else {
            let result = ScanResult(error: "设备不支持相机功能")
            scanResultSubject.send(result)
            return scanResultSubject.eraseToAnyPublisher()
        }
        
        // 检查并请求相机权限
        checkAndRequestPermission()
            .sink { [weak self] granted in
                if granted {
                    self?.startCameraSession()
                } else {
                    let result = ScanResult(error: "相机权限被拒绝")
                    self?.scanResultSubject.send(result)
                }
            }
            .store(in: &cancellables)
        
        return scanResultSubject.eraseToAnyPublisher()
    }
    
    func stopScan() {
        print("[ScanManager] 停止扫码")
        
        isScanning = false
        timeoutTimer?.invalidate()
        timeoutTimer = nil
        
        captureSession?.stopRunning()
        captureSession = nil
        previewLayer?.removeFromSuperlayer()
        previewLayer = nil
        
        currentConfig = nil
        cancellables.removeAll()
        
        delegate?.scanServiceDidCancel()
    }
    
    func checkCameraPermission() -> AnyPublisher<Bool, Never> {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        let granted = status == .authorized
        
        return Just(granted).eraseToAnyPublisher()
    }
    
    func requestCameraPermission() -> AnyPublisher<Bool, Never> {
        return Future<Bool, Never> { promise in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    promise(.success(granted))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 私有方法
    
    /**
     * 设置通知监听
     */
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }
    
    /**
     * 检查并请求相机权限
     */
    private func checkAndRequestPermission() -> AnyPublisher<Bool, Never> {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            return Just(true).eraseToAnyPublisher()
            
        case .notDetermined:
            return requestCameraPermission()
            
        case .denied, .restricted:
            return Just(false).eraseToAnyPublisher()
            
        @unknown default:
            return Just(false).eraseToAnyPublisher()
        }
    }
    
    /**
     * 启动相机会话
     */
    private func startCameraSession() {
        print("[ScanManager] 启动相机会话")
        
        guard let config = currentConfig else {
            let result = ScanResult(error: "扫码配置无效")
            scanResultSubject.send(result)
            return
        }
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.setupCaptureSession(config: config)
        }
    }
    
    /**
     * 设置相机捕获会话
     */
    private func setupCaptureSession(config: ScanConfig) {
        guard let device = AVCaptureDevice.default(for: .video) else {
            DispatchQueue.main.async {
                let result = ScanResult(error: "无法访问相机设备")
                self.scanResultSubject.send(result)
            }
            return
        }
        
        do {
            let input = try AVCaptureDeviceInput(device: device)
            let output = AVCaptureMetadataOutput()
            
            captureSession = AVCaptureSession()
            captureSession?.addInput(input)
            captureSession?.addOutput(output)
            
            output.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
            
            // 设置支持的扫码类型
            var metadataTypes: [AVMetadataObject.ObjectType] = []
            switch config.scanType {
            case .qrCode:
                metadataTypes = [.qr]
            case .barCode:
                metadataTypes = [.ean13, .ean8, .code128, .code39, .code93, .upce]
            case .all:
                metadataTypes = [.qr, .ean13, .ean8, .code128, .code39, .code93, .upce]
            }
            output.metadataObjectTypes = metadataTypes
            
            DispatchQueue.main.async {
                self.isScanning = true
                self.delegate?.scanServiceDidStartScanning()
                self.startTimeout(config: config)
                self.captureSession?.startRunning()
            }
            
        } catch {
            DispatchQueue.main.async {
                let result = ScanResult(error: "相机初始化失败: \(error.localizedDescription)")
                self.scanResultSubject.send(result)
            }
        }
    }
    
    /**
     * 启动超时定时器
     */
    private func startTimeout(config: ScanConfig) {
        timeoutTimer = Timer.scheduledTimer(withTimeInterval: config.timeout, repeats: false) { [weak self] _ in
            print("[ScanManager] 扫码超时")
            let result = ScanResult(error: "扫码超时，请重试")
            self?.scanResultSubject.send(result)
            self?.stopScan()
        }
    }
    
    /**
     * 应用进入后台
     */
    @objc private func applicationDidEnterBackground() {
        if isScanning {
            captureSession?.stopRunning()
        }
    }
    
    /**
     * 应用进入前台
     */
    @objc private func applicationWillEnterForeground() {
        if isScanning {
            captureSession?.startRunning()
        }
    }
}

// MARK: - AVCaptureMetadataOutputObjectsDelegate

extension ScanManager: AVCaptureMetadataOutputObjectsDelegate {
    
    func metadataOutput(
        _ output: AVCaptureMetadataOutput,
        didOutput metadataObjects: [AVMetadataObject],
        from connection: AVCaptureConnection
    ) {
        guard let metadataObject = metadataObjects.first as? AVMetadataMachineReadableCodeObject,
              let stringValue = metadataObject.stringValue else {
            return
        }
        
        print("[ScanManager] 扫码成功: \(stringValue)")
        
        // 确定扫码类型
        let scanType: ScanType
        switch metadataObject.type {
        case .qr:
            scanType = .qrCode
        default:
            scanType = .barCode
        }
        
        // 播放提示音和震动
        if currentConfig?.beepEnabled == true {
            AudioServicesPlaySystemSound(1007) // 系统提示音
        }

        if currentConfig?.vibrateEnabled == true {
            AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))
        }
        
        // 发送扫码结果
        let result = ScanResult(result: stringValue, scanType: scanType)
        scanResultSubject.send(result)
        delegate?.scanServiceDidFinishScanning(with: result)
        
        // 停止扫码
        stopScan()
    }
}
