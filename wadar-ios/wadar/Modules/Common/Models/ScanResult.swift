/**
 * 扫码结果模型
 * 定义扫码操作的结果数据结构
 * 
 * <AUTHOR>
 */

import Foundation

/**
 * 扫码类型枚举
 */
enum ScanType: String, CaseIterable {
    case qrCode = "QR_CODE"
    case barCode = "BAR_CODE"
    case all = "ALL"
    
    var displayName: String {
        switch self {
        case .qrCode:
            return "二维码"
        case .barCode:
            return "条形码"
        case .all:
            return "二维码/条形码"
        }
    }
}

/**
 * 扫码结果数据结构
 */
struct ScanResult {
    /// 扫码是否成功
    let success: Bool
    
    /// 扫码内容
    let result: String?
    
    /// 扫码类型
    let scanType: ScanType?
    
    /// 错误信息
    let error: String?
    
    /// 扫码时间戳
    let timestamp: Int64
    
    /**
     * 成功结果初始化
     */
    init(result: String, scanType: ScanType) {
        self.success = true
        self.result = result
        self.scanType = scanType
        self.error = nil
        self.timestamp = Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    /**
     * 失败结果初始化
     */
    init(error: String) {
        self.success = false
        self.result = nil
        self.scanType = nil
        self.error = error
        self.timestamp = Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    /**
     * 取消结果初始化
     */
    static var cancelled: ScanResult {
        return ScanResult(error: "用户取消扫码")
    }
    
    /**
     * 转换为字典格式
     */
    func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "success": success,
            "timestamp": timestamp
        ]
        
        if let result = result {
            dict["result"] = result
        }
        
        if let scanType = scanType {
            dict["scanType"] = scanType.rawValue
        }
        
        if let error = error {
            dict["error"] = error
        }
        
        return dict
    }
}

/**
 * 扫码参数配置
 */
struct ScanConfig {
    /// 扫码类型
    let scanType: ScanType
    
    /// 是否只允许相机扫码（不允许从相册选择）
    let onlyFromCamera: Bool
    
    /// 扫码提示文字
    let prompt: String
    
    /// 是否开启提示音
    let beepEnabled: Bool
    
    /// 是否开启震动
    let vibrateEnabled: Bool
    
    /// 扫码超时时间（秒）
    let timeout: TimeInterval
    
    /**
     * 默认配置初始化
     */
    init(
        scanType: ScanType = .all,
        onlyFromCamera: Bool = false,
        prompt: String = "请扫描二维码或条形码",
        beepEnabled: Bool = true,
        vibrateEnabled: Bool = true,
        timeout: TimeInterval = 30.0
    ) {
        self.scanType = scanType
        self.onlyFromCamera = onlyFromCamera
        self.prompt = prompt
        self.beepEnabled = beepEnabled
        self.vibrateEnabled = vibrateEnabled
        self.timeout = timeout
    }
    
    /**
     * 从字典创建配置
     */
    static func from(dictionary: [String: Any]) -> ScanConfig {
        let scanTypeString = dictionary["scanType"] as? String ?? "all"
        let scanType = ScanType(rawValue: scanTypeString.uppercased()) ?? .all
        
        let onlyFromCamera = dictionary["onlyFromCamera"] as? Bool ?? false
        let prompt = dictionary["prompt"] as? String ?? "请扫描二维码或条形码"
        let beepEnabled = dictionary["beepEnabled"] as? Bool ?? true
        let vibrateEnabled = dictionary["vibrateEnabled"] as? Bool ?? true
        let timeout = dictionary["timeout"] as? TimeInterval ?? 30.0
        
        return ScanConfig(
            scanType: scanType,
            onlyFromCamera: onlyFromCamera,
            prompt: prompt,
            beepEnabled: beepEnabled,
            vibrateEnabled: vibrateEnabled,
            timeout: timeout
        )
    }
}
