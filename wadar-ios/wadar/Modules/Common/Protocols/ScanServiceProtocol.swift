/**
 * 扫码服务协议
 * 定义扫码功能的标准接口
 * 
 * <AUTHOR>
 */

import Foundation
import Combine

/**
 * 扫码服务协议
 * 提供统一的扫码功能接口
 */
protocol ScanServiceProtocol: AnyObject {
    
    /**
     * 开始扫码
     * @param config 扫码配置
     * @returns 扫码结果的Publisher
     */
    func startScan(with config: ScanConfig) -> AnyPublisher<ScanResult, Never>
    
    /**
     * 停止扫码
     */
    func stopScan()
    
    /**
     * 检查相机权限
     * @returns 权限状态的Publisher
     */
    func checkCameraPermission() -> AnyPublisher<Bool, Never>
    
    /**
     * 请求相机权限
     * @returns 权限授权结果的Publisher
     */
    func requestCameraPermission() -> AnyPublisher<Bool, Never>
    
    /**
     * 检查扫码功能是否可用
     * @returns 是否可用
     */
    var isAvailable: Bool { get }
    
    /**
     * 当前扫码状态
     */
    var isScanning: Bool { get }
}

/**
 * 扫码服务委托协议
 * 用于处理扫码过程中的事件
 */
protocol ScanServiceDelegate: AnyObject {
    
    /**
     * 扫码开始
     */
    func scanServiceDidStartScanning()
    
    /**
     * 扫码结束
     * @param result 扫码结果
     */
    func scanServiceDidFinishScanning(with result: ScanResult)
    
    /**
     * 扫码失败
     * @param error 错误信息
     */
    func scanServiceDidFailWithError(_ error: Error)
    
    /**
     * 用户取消扫码
     */
    func scanServiceDidCancel()
}

/**
 * 扫码错误类型
 */
enum ScanError: LocalizedError {
    case cameraNotAvailable
    case permissionDenied
    case scanTimeout
    case invalidResult
    case userCancelled
    case systemError(String)
    
    var errorDescription: String? {
        switch self {
        case .cameraNotAvailable:
            return "相机不可用"
        case .permissionDenied:
            return "相机权限被拒绝"
        case .scanTimeout:
            return "扫码超时"
        case .invalidResult:
            return "扫码结果无效"
        case .userCancelled:
            return "用户取消扫码"
        case .systemError(let message):
            return "系统错误: \(message)"
        }
    }
}
