/**
 * 扫码界面视图
 * 提供原生扫码功能的用户界面
 * 
 * <AUTHOR>
 */

import SwiftUI
import AVFoundation
import Combine

/**
 * 扫码界面视图
 */
struct ScanView: View {
    
    // MARK: - 属性
    
    /// 扫码配置
    let config: ScanConfig
    
    /// 完成回调
    let onCompletion: (ScanResult) -> Void
    
    /// 取消回调
    let onCancel: () -> Void
    
    /// 扫码管理器
    @StateObject private var scanManager = ScanManager.shared
    
    /// 扫码状态
    @State private var isScanning = false
    
    /// 权限状态
    @State private var hasPermission = false
    
    /// 错误信息
    @State private var errorMessage: String?
    
    /// 显示权限设置提示
    @State private var showPermissionAlert = false
    
    /// 取消订阅集合
    @State private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 视图主体
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                Color.black
                    .ignoresSafeArea()
                
                if hasPermission {
                    // 相机预览和扫码界面
                    scanContentView
                } else {
                    // 权限请求界面
                    permissionView
                }
                
                // 顶部工具栏
                VStack {
                    topToolbar
                    Spacer()
                }
                
                // 底部提示信息
                VStack {
                    Spacer()
                    bottomInfoView
                }
            }
            .navigationBarHidden(true)
            .onAppear {
                checkPermissionAndStartScan()
            }
            .onDisappear {
                stopScan()
            }
            .alert("相机权限", isPresented: $showPermissionAlert) {
                Button("设置") {
                    openSettings()
                }
                Button("取消", role: .cancel) {
                    onCancel()
                }
            } message: {
                Text("需要相机权限才能使用扫码功能，请在设置中开启相机权限")
            }
        }
    }
    
    // MARK: - 子视图
    
    /**
     * 顶部工具栏
     */
    private var topToolbar: some View {
        HStack {
            // 取消按钮
            Button(action: {
                onCancel()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .padding()
            }
            
            Spacer()
            
            // 标题
            Text("扫一扫")
                .font(.headline)
                .foregroundColor(.white)
            
            Spacer()
            
            // 占位符保持居中
            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.horizontal)
        .background(Color.black.opacity(0.3))
    }
    
    /**
     * 扫码内容视图
     */
    private var scanContentView: some View {
        ZStack {
            // 相机预览层（这里简化处理，实际需要集成AVFoundation）
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .overlay(
                    // 扫码框
                    scanFrameView
                )
            
            // 加载指示器
            if isScanning {
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                    
                    Text("正在扫描...")
                        .foregroundColor(.white)
                        .padding(.top)
                }
            }
        }
    }
    
    /**
     * 扫码框视图
     */
    private var scanFrameView: some View {
        RoundedRectangle(cornerRadius: 12)
            .stroke(Color.green, lineWidth: 3)
            .frame(width: 250, height: 250)
            .overlay(
                // 扫描线动画
                Rectangle()
                    .fill(Color.green.opacity(0.3))
                    .frame(height: 2)
                    .offset(y: isScanning ? 125 : -125)
                    .animation(
                        isScanning ? 
                        Animation.linear(duration: 2).repeatForever(autoreverses: true) : 
                        .default,
                        value: isScanning
                    )
            )
    }
    
    /**
     * 权限请求视图
     */
    private var permissionView: some View {
        VStack(spacing: 20) {
            Image(systemName: "camera.fill")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("需要相机权限")
                .font(.title2)
                .foregroundColor(.white)
            
            Text("请允许访问相机以使用扫码功能")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
            
            Button("授权相机权限") {
                requestPermission()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }
    
    /**
     * 底部信息视图
     */
    private var bottomInfoView: some View {
        VStack(spacing: 12) {
            Text(config.prompt)
                .font(.body)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal)
            }
        }
        .padding(.bottom, 50)
        .background(
            LinearGradient(
                colors: [Color.clear, Color.black.opacity(0.6)],
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    // MARK: - 私有方法
    
    /**
     * 检查权限并开始扫码
     */
    private func checkPermissionAndStartScan() {
        scanManager.checkCameraPermission()
            .sink { permission in
                hasPermission = permission
                if permission {
                    startScan()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 请求相机权限
     */
    private func requestPermission() {
        scanManager.requestCameraPermission()
            .sink { granted in
                hasPermission = granted
                if granted {
                    startScan()
                } else {
                    showPermissionAlert = true
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 开始扫码
     */
    private func startScan() {
        isScanning = true
        errorMessage = nil
        
        scanManager.startScan(with: config)
            .sink { result in
                isScanning = false
                onCompletion(result)
            }
            .store(in: &cancellables)
    }
    
    /**
     * 停止扫码
     */
    private func stopScan() {
        isScanning = false
        scanManager.stopScan()
        cancellables.removeAll()
    }
    
    /**
     * 打开系统设置
     */
    private func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - 预览

struct ScanView_Previews: PreviewProvider {
    static var previews: some View {
        ScanView(
            config: ScanConfig(),
            onCompletion: { result in
                print("扫码结果: \(result)")
            },
            onCancel: {
                print("取消扫码")
            }
        )
    }
}
