//
//  WebViewContainerView.swift
//  wadar
//
//  Created by AI Assistant on 2025/6/25.
//

import SwiftUI

struct WebViewContainerView: View {
    let url: URL
    @ObservedObject var callVM: CallViewModel
    @ObservedObject var navigationManager: NavigationManager
    @State private var showVoIPStatus = false
    @State private var voipStatusMessage: String?
    
    var body: some View {
        ZStack {
            OptimizedWebView(url: url)
                .ignoresSafeArea(edges: .all)
            
            // VoIP状态指示器和刷新按钮
            VStack {
                if showVoIPStatus {
                    HStack {
                        Image(systemName: getVoIPStatusIcon())
                            .foregroundColor(getVoIPStatusColor())
                        Text(voipStatusMessage ?? "")
                            .font(.caption)
                            .foregroundColor(.white)

                        if callVM.voipStatus == .failed || callVM.voipStatus == .disconnected {
                            Button("重试") {
                                callVM.manualReconnect()
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(4)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(getVoIPStatusBackgroundColor())
                    .cornerRadius(8)
                    .shadow(radius: 2)
                }
                
                Spacer()
                
                // 右下角功能按钮组
                HStack {
                    Spacer()
                    VStack {
                        Spacer()

                        // 设备配网按钮
                        // Button(action: {
                        //     print("[WebViewContainer] 用户点击设备配网按钮")
                        //     navigationManager.navigateToESPProvision()
                        // }) {
                        //     Image(systemName: "wifi.router")
                        //         .font(.system(size: 16, weight: .medium))
                        //         .foregroundColor(.white)
                        //         .frame(width: 40, height: 40)
                        //         .background(Color.green.opacity(0.8))
                        //         .clipShape(Circle())
                        //         .shadow(radius: 3)
                        // }
                        // .padding(.bottom, 10)

                        // 页面刷新按钮
                        Button(action: {
                            print("[WebViewContainer] 用户手动强制刷新WebView")
                            WebViewManager.shared.forceReloadCurrentWebView()
                        }) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 40, height: 40)
                                .background(Color.blue.opacity(0.8))
                                .clipShape(Circle())
                                .shadow(radius: 3)
                        }
                        .padding(.bottom, 30)
                        .padding(.trailing, 20)
                    }
                }
            }
            .padding(.top, 50)
            .padding(.horizontal, 16)
        }
        .navigationBarHidden(true)
        .onAppear {
            setupNotifications()
        }
        .onDisappear {
            removeNotifications()
        }
    }
    
    // MARK: - VoIP状态相关方法
    private func getVoIPStatusIcon() -> String {
        switch callVM.voipStatus {
        case .connected:
            return "checkmark.circle.fill"
        case .connecting, .reconnecting:
            return "arrow.clockwise"
        case .failed, .disconnected:
            return "exclamationmark.triangle.fill"
        }
    }
    
    private func getVoIPStatusColor() -> Color {
        switch callVM.voipStatus {
        case .connected:
            return .green
        case .connecting, .reconnecting:
            return .orange
        case .failed, .disconnected:
            return .red
        }
    }
    
    private func getVoIPStatusBackgroundColor() -> Color {
        switch callVM.voipStatus {
        case .connected:
            return Color.green.opacity(0.8)
        case .connecting, .reconnecting:
            return Color.orange.opacity(0.8)
        case .failed, .disconnected:
            return Color.red.opacity(0.8)
        }
    }
    
    // MARK: - 通知设置
    private func setupNotifications() {
        // 监听VoIP可用通知
        NotificationCenter.default.addObserver(forName: .VoIPAvailableNotification, object: nil, queue: .main) { _ in
            print("[WebViewContainer] VoIP连接成功")
            self.voipStatusMessage = "通话功能已就绪"
            self.showVoIPStatus = true

            // 3秒后自动隐藏成功提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                if self.voipStatusMessage == "通话功能已就绪" {
                    self.showVoIPStatus = false
                }
            }
        }

        // 监听VoIP不可用通知
        NotificationCenter.default.addObserver(forName: .VoIPUnavailableNotification, object: nil, queue: .main) { notification in
            let reason = notification.userInfo?["reason"] as? String ?? "未知原因"
            print("[WebViewContainer] VoIP不可用: \(reason)")
            self.voipStatusMessage = "通话功能暂不可用: \(reason)"
            self.showVoIPStatus = true
        }

        // 注意：来电和外呼通知已在ContentView中统一处理，这里不需要重复监听
    }
    
    private func removeNotifications() {
        NotificationCenter.default.removeObserver(self)
    }
}

#Preview {
    WebViewContainerView(
        url: URL(string: "https://example.com")!,
        callVM: CallViewModel.shared,
        navigationManager: NavigationManager()
    )
}
