import SwiftUI

struct CallOutgoingView: View {
    @ObservedObject var viewModel : CallViewModel
    @State private var isMicrophoneOn: Bool = true
    @State private var isSpeakerOn: Bool = false
    @State private var displayName: String = ""
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack {
            VStack {
                // 头像（可替换为实际图片）
                // Image("call_header")
                //     .resizable()
                //     .frame(width: 120, height: 120)
                //     .padding(.top, 60)
                Image(systemName: "person.crop.circle.fill") // 使用系统头像图标
                    .resizable()
                    .frame(width: 120, height: 120)
                    .foregroundColor(.white)
                    .padding(.top, 60)
                Text(displayName)
                    .font(.system(size: 24, weight: .regular))
                    .foregroundColor(.white)
                    .padding(.top, 20)
            }
            Spacer()
            VStack {
                Text("正在呼叫...")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            Spacer()
            HStack {
                Spacer()
                VStack {
                    Button(action: {
                        // 挂断操作
                        hangUpCall()
                    }) {
                        Image(systemName: "phone.down.fill") // 使用系统挂断图标
                            .resizable()
                            .frame(width: 68, height: 68)
                            .foregroundColor(.red)
                    }
                    Text("挂断")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .padding(.top, 5)
                }
                Spacer()
            }
            .padding(.bottom, 80)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color("call_background"))
        .onAppear {
            // 获取对方显示名，实际项目中请替换为真实数据
            displayName = getRemoteDisplayName()
        }
    }

    func hangUpCall() {
        // 挂断通话逻辑，实际项目中请调用VoIP SDK
        self.viewModel.terminateCall()
        // 注意：不要在这里直接dismiss，让VoIPCallContainerView处理通话结束后的导航
        // presentationMode.wrappedValue.dismiss() // 移除这行，让容器视图处理
    }

    func getRemoteDisplayName() -> String {
        return viewModel.callNum
    }
}

#Preview {
    CallOutgoingView(viewModel: CallViewModel.shared)
}
