//
//  VoIPCallContainerView.swift
//  wadar
//
//  Created by guan on 2025/7/3.
//  VoIP通话容器视图 - 使用modal presentation方式
//

import SwiftUI

/**
 * VoIP通话容器视图
 * 负责管理不同类型的VoIP通话界面，使用modal presentation方式
 */
struct VoIPCallContainerView: View {
    let callType: VoIPCallType
    let number: String
    let onComplete: (VoIPCallResult) -> Void
    
    @StateObject private var callViewModel = CallViewModel.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var currentCallState: VoIPCallType
    
    init(callType: VoIPCallType, number: String, onComplete: @escaping (VoIPCallResult) -> Void) {
        self.callType = callType
        self.number = number
        self.onComplete = onComplete
        self._currentCallState = State(initialValue: callType)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // 根据当前通话状态显示不同的界面
                switch currentCallState {
                case .outgoing:
                    CallOutgoingView(viewModel: callViewModel)
                        .onAppear {
                            print("[VoIPCallContainerView] 显示外呼界面")
                        }
                case .incoming:
                    CallIncomingView(viewModel: callViewModel)
                        .onAppear {
                            print("[VoIPCallContainerView] 显示来电界面")
                        }
                case .connected:
                    CallConnectView(viewModel: callViewModel)
                        .onAppear {
                            print("[VoIPCallContainerView] 显示通话界面")
                        }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            setupNotifications()
        }
        .onDisappear {
            removeNotifications()
        }
    }
    
    // MARK: - 通知设置
    
    private func setupNotifications() {
        // 监听通话连接通知
        NotificationCenter.default.addObserver(
            forName: .callConnected,
            object: nil,
            queue: .main
        ) { _ in
            print("[VoIPCallContainerView] 通话已连接，切换到通话界面")
            currentCallState = .connected
        }
        
        // 监听通话结束通知
        NotificationCenter.default.addObserver(
            forName: .callEnded,
            object: nil,
            queue: .main
        ) { _ in
            print("[VoIPCallContainerView] 通话已结束，准备关闭界面")
            handleCallEnded()
        }
        
        // 监听来电通知（用于处理来电场景）
        NotificationCenter.default.addObserver(
            forName: .incomingCallReceived,
            object: nil,
            queue: .main
        ) { _ in
            if currentCallState != .incoming {
                print("[VoIPCallContainerView] 收到来电，切换到来电界面")
                currentCallState = .incoming
            }
        }
    }
    
    private func removeNotifications() {
        NotificationCenter.default.removeObserver(self, name: .callConnected, object: nil)
        NotificationCenter.default.removeObserver(self, name: .callEnded, object: nil)
        NotificationCenter.default.removeObserver(self, name: .incomingCallReceived, object: nil)
    }
    
    // MARK: - 事件处理
    
    private func handleCallEnded() {
        print("[VoIPCallContainerView] 处理通话结束")
        
        // 关闭界面
        presentationMode.wrappedValue.dismiss()
        
        // 通知完成
        onComplete(.completed([
            "number": number,
            "endTime": Date().timeIntervalSince1970
        ]))
    }
    
    private func handleCallCancelled() {
        print("[VoIPCallContainerView] 处理通话取消")
        
        // 关闭界面
        presentationMode.wrappedValue.dismiss()
        
        // 通知取消
        onComplete(.cancelled)
    }
    
    private func handleCallFailed(_ error: String) {
        print("[VoIPCallContainerView] 处理通话失败: \(error)")
        
        // 关闭界面
        presentationMode.wrappedValue.dismiss()
        
        // 通知失败
        onComplete(.failed(error))
    }
}

// MARK: - 预览

#Preview {
    VoIPCallContainerView(
        callType: .outgoing,
        number: "1008"
    ) { result in
        print("通话结果: \(result)")
    }
}
