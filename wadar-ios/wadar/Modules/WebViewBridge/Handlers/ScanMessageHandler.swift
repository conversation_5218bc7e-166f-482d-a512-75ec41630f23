/**
 * 扫码消息处理器
 * 处理来自WebView的扫码相关消息
 * 
 * <AUTHOR>
 */

import Foundation
import SwiftUI
import Combine

/**
 * 扫码消息处理器
 * 实现MessageHandler协议，处理扫码相关的WebView消息
 */
class ScanMessageHandler: MessageHandler {
    
    // MARK: - MessageHandler 协议实现
    
    let handlerName = "ScanMessageHandler"
    
    let supportedActions = [
        "scan.scanCode",
        "scan.getStatus"
    ]
    
    // MARK: - 私有属性
    
    /// 扫码管理器
    private let scanManager = ScanManager.shared
    
    /// 当前扫码视图控制器
    private var currentScanViewController: UIViewController?
    
    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    init() {
        print("[ScanMessageHandler] 扫码消息处理器初始化完成")
    }
    
    deinit {
        cancellables.removeAll()
        print("[ScanMessageHandler] 扫码消息处理器已销毁")
    }
    
    // MARK: - 消息处理
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "scanCode":
            handleScanCode(message: message, completion: completion)
        case "getStatus":
            handleGetStatus(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的扫码操作: \(method)"))
        }
    }
    
    // MARK: - 具体处理方法
    
    /**
     * 处理扫码请求
     */
    private func handleScanCode(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 处理扫码请求")
        
        // 检查扫码功能是否可用
        guard scanManager.isAvailable else {
            completion(.failure(error: "设备不支持扫码功能"))
            return
        }
        
        // 如果正在扫码，先停止
        if scanManager.isScanning {
            scanManager.stopScan()
        }
        
        // 解析扫码配置
        let config = ScanConfig.from(dictionary: message.data)
        print("[ScanMessageHandler] 扫码配置: \(config)")
        
        // 在主线程显示扫码界面
        DispatchQueue.main.async { [weak self] in
            self?.presentScanView(config: config, completion: completion)
        }
    }
    
    /**
     * 处理获取扫码状态请求
     */
    private func handleGetStatus(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 获取扫码状态")
        
        let statusData: [String: Any] = [
            "isAvailable": scanManager.isAvailable,
            "isScanning": scanManager.isScanning,
            "hasPermission": checkCameraPermission()
        ]
        
        completion(.success(data: statusData))
    }
    
    // MARK: - 私有方法
    
    /**
     * 显示扫码界面
     */
    private func presentScanView(config: ScanConfig, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            completion(.failure(error: "无法获取根视图控制器"))
            return
        }
        
        // 创建扫码视图
        let scanView = ScanView(
            config: config,
            onCompletion: { [weak self] result in
                self?.handleScanResult(result, completion: completion)
            },
            onCancel: { [weak self] in
                self?.handleScanCancel(completion: completion)
            }
        )
        
        // 包装为UIViewController
        let hostingController = UIHostingController(rootView: scanView)
        hostingController.modalPresentationStyle = .fullScreen
        
        // 保存引用
        currentScanViewController = hostingController
        
        // 显示扫码界面
        rootViewController.present(hostingController, animated: true) {
            print("[ScanMessageHandler] 扫码界面已显示")
        }
    }
    
    /**
     * 处理扫码结果
     */
    private func handleScanResult(_ result: ScanResult, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 扫码结果: \(result)")
        
        // 关闭扫码界面
        dismissScanView()
        
        // 返回结果
        let resultData = result.toDictionary()
        completion(.success(data: resultData))
    }
    
    /**
     * 处理扫码取消
     */
    private func handleScanCancel(completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 用户取消扫码")
        
        // 关闭扫码界面
        dismissScanView()
        
        // 返回取消结果
        let cancelResult = ScanResult.cancelled
        let resultData = cancelResult.toDictionary()
        completion(.success(data: resultData))
    }
    
    /**
     * 关闭扫码界面
     */
    private func dismissScanView() {
        DispatchQueue.main.async { [weak self] in
            self?.currentScanViewController?.dismiss(animated: true) {
                print("[ScanMessageHandler] 扫码界面已关闭")
                self?.currentScanViewController = nil
            }
        }
    }
    
    /**
     * 检查相机权限
     */
    private func checkCameraPermission() -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }
}

// MARK: - 扩展：错误处理

extension ScanMessageHandler {
    
    /**
     * 处理扫码错误
     */
    private func handleScanError(_ error: Error, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[ScanMessageHandler] 扫码错误: \(error)")
        
        dismissScanView()
        
        let errorMessage: String
        if let scanError = error as? ScanError {
            errorMessage = scanError.localizedDescription
        } else {
            errorMessage = "扫码失败: \(error.localizedDescription)"
        }
        
        completion(.failure(error: errorMessage))
    }
    
    /**
     * 验证扫码参数
     */
    private func validateScanParameters(_ data: [String: Any]) -> (isValid: Bool, error: String?) {
        // 这里可以添加参数验证逻辑
        return (true, nil)
    }
}
