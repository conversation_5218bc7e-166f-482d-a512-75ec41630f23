/**
 * VoIP消息处理器
 * 处理VoIP相关的消息
 * 
 * <AUTHOR>
 */

import Foundation
import SwiftUI

/**
 * VoIP消息处理器类
 * 实现VoIP相关功能的消息处理
 */
class VoIPMessageHandler: MessageHandler {
    
    var handlerName: String = "VoIPMessageHandler"
    
    var supportedActions: [String] = [
        "voip.call",
        "voip.hangup",
        "voip.answer",
        "voip.reject",
        "voip.getStatus",
        "voip.login",
        "voip.logout"
    ]

    // 保存当前显示的通话界面控制器
    private var currentCallController: UIViewController?

    init() {
        setupIncomingCallNotification()
    }

    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "call":
            handleVoIPCall(message: message, completion: completion)
        case "hangup":
            handleVoIPHangup(message: message, completion: completion)
        case "answer":
            handleVoIPAnswer(message: message, completion: completion)
        case "reject":
            handleVoIPReject(message: message, completion: completion)
        case "getStatus":
            handleGetVoIPStatus(message: message, completion: completion)
        case "login":
            handleVoIPLogin(message: message, completion: completion)
        case "logout":
            handleVoIPLogout(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的VoIP操作: \(method)"))
        }
    }
    
    // MARK: - VoIP操作处理
    
    /**
     * 处理VoIP呼叫
     */
    private func handleVoIPCall(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let number = message.data["number"] as? String, !number.isEmpty else {
            completion(.failure(error: "VoIP呼叫缺少号码参数"))
            return
        }
        
        let token = message.data["token"] as? String
        let displayName = message.data["displayName"] as? String
        
        print("[VoIPMessageHandler] 处理VoIP呼叫请求 - 号码: \(number), Token: \(token ?? "无")")
        
        // 在主线程中处理UI相关操作
        DispatchQueue.main.async {
            self.initiateVoIPCall(number: number, token: token, displayName: displayName, completion: completion)
        }
    }
    
    /**
     * 发起VoIP呼叫
     */
    private func initiateVoIPCall(
        number: String,
        token: String?,
        displayName: String?,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        print("[VoIPMessageHandler] 通过消息处理器发起VoIP呼叫: \(number)")
        
        // 设置呼叫号码到CallViewModel
        CallViewModel.shared.callNum = number
        
        // 如果有token，可以在这里处理认证逻辑
        if let token = token {
            print("[VoIPMessageHandler] 使用Token进行认证: \(token)")
            // 这里可以添加token验证逻辑
        }
        
        // 检查VoIP登录状态，如果未登录则先登录
        if !CallViewModel.shared.loggedIn {
            print("[VoIPMessageHandler] VoIP未登录，尝试使用当前用户信息登录")
            
            // 获取当前用户的VoIP账号信息
            if let userData = UserManager.shared.getUser(),
               let voipNumber = userData.member.voipNumber,
               let voipPassword = userData.member.voipPassword {
                print("[VoIPMessageHandler] 找到VoIP账号信息，开始登录: \(voipNumber)")
                
                // 设置账号信息并登录
                CallViewModel.shared.username = voipNumber
                CallViewModel.shared.passwd = voipPassword
                CallViewModel.shared.login()
                
                // 等待登录完成后再发起呼叫
                waitForVoIPLogin { success in
                    if success {
                        self.performVoIPCall(number: number, token: token, completion: completion)
                    } else {
                        completion(.failure(error: "VoIP登录失败，无法发起呼叫"))
                    }
                }
            } else {
                print("[VoIPMessageHandler] 未找到VoIP账号信息，无法发起呼叫")
                completion(.failure(error: "VoIP账号未配置，无法发起呼叫"))
                return
            }
        } else {
            // 已登录，直接发起呼叫
            performVoIPCall(number: number, token: token, completion: completion)
        }
    }
    
    /**
     * 执行VoIP呼叫
     */
    private func performVoIPCall(
        number: String,
        token: String?,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        print("[VoIPMessageHandler] 执行VoIP呼叫: \(number)")

        // 发起呼叫
        CallViewModel.shared.outgoingCall()

        // 使用modal presentation方式显示外呼界面
        presentVoIPCallView(callType: .outgoing, number: number, token: token, completion: completion)
    }
    
    /**
     * 处理VoIP挂断
     */
    private func handleVoIPHangup(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP挂断请求")
        
        DispatchQueue.main.async {
            // 执行挂断操作
            CallViewModel.shared.terminateCall()
            
            let responseData: [String: Any] = [
                "action": "hangup",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理VoIP接听
     */
    private func handleVoIPAnswer(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP接听请求")
        
        DispatchQueue.main.async {
            // 执行接听操作
            CallViewModel.shared.acceptCall()
            
            let responseData: [String: Any] = [
                "action": "answer",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理VoIP拒绝
     */
    private func handleVoIPReject(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP拒绝请求")
        
        DispatchQueue.main.async {
            // 执行拒绝操作 - 使用terminateCall来拒绝来电
            CallViewModel.shared.terminateCall()

            let responseData: [String: Any] = [
                "action": "reject",
                "timestamp": Date().timeIntervalSince1970
            ]

            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理获取VoIP状态
     */
    private func handleGetVoIPStatus(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理获取VoIP状态请求")
        
        let callViewModel = CallViewModel.shared
        let responseData: [String: Any] = [
            "isLoggedIn": callViewModel.loggedIn,
            "isInCall": callViewModel.mCore.currentCall != nil,
            "username": callViewModel.username,
            "accountState": callViewModel.mCore.defaultAccount?.state.rawValue ?? -1,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理VoIP登录
     */
    private func handleVoIPLogin(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let username = message.data["username"] as? String,
              let password = message.data["password"] as? String else {
            completion(.failure(error: "VoIP登录缺少用户名或密码"))
            return
        }
        
        print("[VoIPMessageHandler] 处理VoIP登录请求: \(username)")
        
        DispatchQueue.main.async {
            CallViewModel.shared.username = username
            CallViewModel.shared.passwd = password
            CallViewModel.shared.login()
            
            // 等待登录结果
            self.waitForVoIPLogin { success in
                if success {
                    let responseData: [String: Any] = [
                        "action": "login",
                        "username": username,
                        "timestamp": Date().timeIntervalSince1970
                    ]
                    completion(.success(data: responseData))
                } else {
                    completion(.failure(error: "VoIP登录失败"))
                }
            }
        }
    }
    
    /**
     * 处理VoIP登出
     */
    private func handleVoIPLogout(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[VoIPMessageHandler] 处理VoIP登出请求")
        
        DispatchQueue.main.async {
            CallViewModel.shared.unregister()
            
            let responseData: [String: Any] = [
                "action": "logout",
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    // MARK: - 辅助方法
    
    /**
     * 等待VoIP登录完成
     */
    private func waitForVoIPLogin(completion: @escaping (Bool) -> Void) {
        let maxWaitTime = 5.0 // 最大等待5秒
        let checkInterval = 0.1 // 每100ms检查一次
        var elapsedTime = 0.0
        
        func checkLoginStatus() {
            if CallViewModel.shared.loggedIn && CallViewModel.shared.mCore.defaultAccount?.state == .Ok {
                print("[VoIPMessageHandler] VoIP登录成功")
                completion(true)
            } else if elapsedTime >= maxWaitTime {
                print("[VoIPMessageHandler] VoIP登录超时")
                completion(false)
            } else {
                elapsedTime += checkInterval
                DispatchQueue.main.asyncAfter(deadline: .now() + checkInterval) {
                    checkLoginStatus()
                }
            }
        }
        
        checkLoginStatus()
    }

    // MARK: - 来电通知设置

    /**
     * 设置来电通知监听
     */
    private func setupIncomingCallNotification() {
        // 监听来电通知，使用modal presentation方式显示来电界面
        NotificationCenter.default.addObserver(
            forName: .incomingCallReceived,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            let userInfo = notification.userInfo
            let callerId = userInfo?["caller_id"] as? String ?? "未知号码"
            let source = userInfo?["source"] as? String ?? "unknown"

            print("[VoIPMessageHandler] 收到来电通知，来电者: \(callerId), 来源: \(source)")

            // 使用modal presentation方式显示来电界面
            self.presentIncomingCallView(callerId: callerId)
        }
    }

    /**
     * 显示来电界面
     */
    private func presentIncomingCallView(callerId: String) {
        // 如果已经有通话界面在显示，不重复显示
        if currentCallController != nil {
            print("[VoIPMessageHandler] 已有通话界面在显示，忽略新的来电")
            return
        }

        // 使用modal presentation方式显示来电界面
        presentVoIPCallView(
            callType: .incoming,
            number: callerId,
            token: nil
        ) { result in
            print("[VoIPMessageHandler] 来电界面结果: \(result)")
        }
    }

    // MARK: - Modal Presentation方法

    /**
     * 使用modal presentation方式显示VoIP通话界面
     */
    private func presentVoIPCallView(
        callType: VoIPCallType,
        number: String,
        token: String?,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        // 获取当前的根视图控制器
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            completion(.failure(error: "无法获取根视图控制器"))
            return
        }

        // 创建VoIP通话视图
        let voipCallView = createVoIPCallView(callType: callType, number: number) { [weak self] result in
            self?.handleVoIPCallResult(result: result, completion: completion)
        }

        // 包装为UIHostingController
        let hostingController = UIHostingController(rootView: voipCallView)
        hostingController.modalPresentationStyle = .fullScreen

        // 保存控制器引用
        currentCallController = hostingController

        // 呈现VoIP通话页面
        rootViewController.present(hostingController, animated: true) {
            print("[VoIPMessageHandler] VoIP通话页面已打开: \(callType)")
        }
    }

    /**
     * 创建VoIP通话视图
     */
    private func createVoIPCallView(
        callType: VoIPCallType,
        number: String,
        onComplete: @escaping (VoIPCallResult) -> Void
    ) -> some View {
        return VoIPCallContainerView(
            callType: callType,
            number: number,
            onComplete: onComplete
        )
    }

    /**
     * 处理VoIP通话结果
     */
    private func handleVoIPCallResult(
        result: VoIPCallResult,
        completion: @escaping (MessageHandlerResult) -> Void
    ) {
        // 清除控制器引用
        currentCallController = nil

        switch result {
        case .completed(let data):
            print("[VoIPMessageHandler] VoIP通话完成: \(data)")
            completion(.success(data: [
                "success": true,
                "message": "通话完成",
                "data": data
            ]))
        case .cancelled:
            print("[VoIPMessageHandler] VoIP通话已取消")
            completion(.success(data: [
                "success": false,
                "message": "用户取消了通话",
                "cancelled": true
            ]))
        case .failed(let error):
            print("[VoIPMessageHandler] VoIP通话失败: \(error)")
            completion(.failure(error: "VoIP通话失败: \(error)"))
        }
    }
}

// MARK: - VoIP通话类型枚举

enum VoIPCallType {
    case outgoing
    case incoming
    case connected
}

// MARK: - VoIP通话结果枚举

enum VoIPCallResult {
    case completed([String: Any])
    case cancelled
    case failed(String)
}
