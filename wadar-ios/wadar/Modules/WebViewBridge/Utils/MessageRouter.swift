/**
 * 消息路由器
 * 负责将消息路由到对应的处理器
 * 
 * <AUTHOR>
 */

import Foundation

/**
 * 消息处理器协议
 */
protocol MessageHandler: AnyObject {
    /**
     * 处理消息
     * @param message 消息对象
     * @param completion 完成回调
     */
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void)
    
    /**
     * 获取处理器名称
     */
    var handlerName: String { get }
    
    /**
     * 获取支持的action列表
     */
    var supportedActions: [String] { get }
}

/**
 * 消息处理结果
 */
enum MessageHandlerResult {
    case success(data: [String: Any]?)
    case failure(error: String)
    case noResponse // 不需要响应（如通知类型消息）
}

/**
 * 消息路由器类
 * 管理消息处理器的注册和路由
 */
class MessageRouter {
    private var handlers: [String: MessageHandler] = [:]
    private var responseManager: ResponseManager?
    private let messageProtocol = MessageProtocol.shared
    
    init() {
        // 注册默认处理器
        registerDefaultHandlers()
    }
    
    /**
     * 设置响应管理器
     */
    func setResponseManager(_ responseManager: ResponseManager) {
        self.responseManager = responseManager
    }
    
    /**
     * 注册默认处理器
     */
    private func registerDefaultHandlers() {
        // 注册VoIP处理器
        let voipHandler = VoIPMessageHandler()
        for action in voipHandler.supportedActions {
            registerHandler(voipHandler, for: action)
        }
        
        // 注册导航处理器
        let navigationHandler = NavigationMessageHandler()
        for action in navigationHandler.supportedActions {
            registerHandler(navigationHandler, for: action)
        }
        
        // 注册设备处理器
        let deviceHandler = DeviceMessageHandler()
        for action in deviceHandler.supportedActions {
            registerHandler(deviceHandler, for: action)
        }
        
        // 注册存储处理器
        let storageHandler = StorageMessageHandler()
        for action in storageHandler.supportedActions {
            registerHandler(storageHandler, for: action)
        }
        
        // 注册用户处理器
        let userHandler = UserMessageHandler()
        for action in userHandler.supportedActions {
            registerHandler(userHandler, for: action)
        }
        
        // 注册系统处理器
        let systemHandler = SystemMessageHandler()
        for action in systemHandler.supportedActions {
            registerHandler(systemHandler, for: action)
        }

        // 注册WiFi处理器
        let wifiHandler = WiFiMessageHandler()
        for action in wifiHandler.supportedActions {
            registerHandler(wifiHandler, for: action)
        }

        // 注册扫码处理器
        let scanHandler = ScanMessageHandler()
        for action in scanHandler.supportedActions {
            registerHandler(scanHandler, for: action)
        }

        print("[MessageRouter] 默认处理器注册完成，共 \(handlers.count) 个处理器")
    }
    
    /**
     * 注册消息处理器
     */
    func registerHandler(_ handler: MessageHandler, for action: String) {
        handlers[action] = handler
        print("[MessageRouter] 注册处理器: \(action) -> \(handler.handlerName)")
    }
    
    /**
     * 注销消息处理器
     */
    func unregisterHandler(for action: String) {
        if let handler = handlers.removeValue(forKey: action) {
            print("[MessageRouter] 注销处理器: \(action) -> \(handler.handlerName)")
        }
    }
    
    /**
     * 检查是否有处理器处理指定action
     */
    func hasHandler(for action: String) -> Bool {
        return handlers[action] != nil
    }
    
    /**
     * 获取已注册的action列表
     */
    func getRegisteredActions() -> [String] {
        return Array(handlers.keys).sorted()
    }
    
    /**
     * 移除所有处理器
     */
    func removeAllHandlers() {
        handlers.removeAll()
        print("[MessageRouter] 所有处理器已移除")
    }
    
    /**
     * 路由消息到对应的处理器
     */
    func routeMessage(_ message: AppMessage) {
        print("[MessageRouter] 路由消息: \(messageProtocol.getMessageSummary(message))")
        
        // 查找对应的处理器
        guard let handler = handlers[message.action] else {
            print("[MessageRouter] 未找到处理器: \(message.action)")
            sendErrorResponse(
                requestId: message.id,
                error: "未找到处理器: \(message.action)"
            )
            return
        }
        
        // 调用处理器处理消息
        handler.handleMessage(message) { [weak self] result in
            self?.handleResult(result, for: message)
        }
    }
    
    /**
     * 处理处理器的结果
     */
    private func handleResult(_ result: MessageHandlerResult, for message: AppMessage) {
        guard let responseManager = responseManager else {
            print("[MessageRouter] 响应管理器未设置")
            return
        }
        
        // 只有请求类型的消息需要响应
        guard message.type == .request else {
            return
        }
        
        switch result {
        case .success(let data):
            responseManager.sendSuccessResponse(
                requestId: message.id,
                data: data
            )
            
        case .failure(let error):
            responseManager.sendErrorResponse(
                requestId: message.id,
                error: error
            )
            
        case .noResponse:
            // 不发送响应
            break
        }
    }
    
    /**
     * 发送错误响应
     */
    private func sendErrorResponse(requestId: String, error: String) {
        responseManager?.sendErrorResponse(
            requestId: requestId,
            error: error
        )
    }
    
    /**
     * 获取路由器状态
     */
    func getStatus() -> [String: Any] {
        var handlerInfo: [String: String] = [:]
        for (action, handler) in handlers {
            handlerInfo[action] = handler.handlerName
        }
        
        return [
            "handlerCount": handlers.count,
            "registeredActions": getRegisteredActions(),
            "handlers": handlerInfo
        ]
    }
    
    /**
     * 批量注册处理器
     */
    func registerHandlers(_ handlerMappings: [String: MessageHandler]) {
        for (action, handler) in handlerMappings {
            registerHandler(handler, for: action)
        }
    }
    
    /**
     * 根据服务名获取相关的action列表
     */
    func getActionsForService(_ serviceName: String) -> [String] {
        return getRegisteredActions().filter { action in
            let (service, _) = messageProtocol.parseAction(action)
            return service == serviceName
        }
    }
    
    /**
     * 获取所有服务名列表
     */
    func getAvailableServices() -> [String] {
        let services = Set(getRegisteredActions().map { action in
            let (service, _) = messageProtocol.parseAction(action)
            return service
        })
        return Array(services).sorted()
    }
    
    /**
     * 检查服务是否可用
     */
    func isServiceAvailable(_ serviceName: String) -> Bool {
        return getAvailableServices().contains(serviceName)
    }
    
    /**
     * 获取处理器信息
     */
    func getHandlerInfo(for action: String) -> [String: Any]? {
        guard let handler = handlers[action] else {
            return nil
        }
        
        return [
            "handlerName": handler.handlerName,
            "action": action,
            "supportedActions": handler.supportedActions
        ]
    }
}
