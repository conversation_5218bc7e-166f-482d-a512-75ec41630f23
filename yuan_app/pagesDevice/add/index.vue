<template>
	<view class="device-index-page">
		<view class="top-header-line"></view>

		<view style="text-align: left; margin: 120rpx 60rpx; 0 60rpx">
			<!-- <image class="img-center-bg" :src="`${staticUrl}/images/device/add-device-pic1.png`"></image> -->
			<view style="font-size:32rpx;color:#000000;">如何添加设备</view>
			<view style="font-size:24rpx;color:#000000">
				<view style="padding: 30rpx 0;">
					1、请确保设备<text style="color:#01B09A">已插电</text>或<text style="color:#01B09A">电量充足</text>
				</view>
				<view>
					2、请扫描<text style="color:#01B09A">设备背面</text>或<text style="color:#01B09A">设备包装盒上</text>的二维码/条形码进行设备添加
				</view>
			</view>
			<!-- <u-checkbox-group style="position: relative; top: 40rpx; left: 30rpx;">
				<u-checkbox v-model="checked" shape="circle" style="font-size: 22rpx;">已完成上述操作</u-checkbox>
			</u-checkbox-group> -->
		</view>

		<!-- #ifdef MP-WEIXIN -->
		<view class="footer-btns">
			<u-button shape="circle" class="scan-code-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="scan">扫一扫</u-button>
			<!-- <u-button shape="circle" class="scan-code-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="scan">确认</u-button> -->
		</view>
		<!-- #endif -->

	</view>
</template>

<script>
	import {isUrl,getUrlParam} from '../../utils/util'
	import { AppServiceAdapter } from '../../utils/appBridge.js'
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				checked: false
			}
		},
		onLoad(option) {//默认加载
			// this.login();
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif
			if (option.devCode) {
				this.vali(option.devCode)
			}
		},
		onShow() {
			// this.fetchFuwuCount();
		},
		methods: {
			handleCheckedClick() {
				this.checked = this.checked === true ? false : true
			},
			gotoUrl(url) {
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			async scan() {
				// 检测是否在App的WebView环境中
				if (this.isInAppWebView()) {
					console.log('[扫码] 检测到App环境，使用原生扫码功能')
					await this.scanWithNativeApp()
				} else {
					console.log('[扫码] 检测到非App环境，使用uni.scanCode')
					this.scanWithUniApp()
				}
			},

			/**
			 * 检测是否在App的WebView环境中
			 * @returns {boolean}
			 */
			isInAppWebView() {
				// #ifdef H5
				// 检查URL参数中是否有App标识
				const urlParams = new URLSearchParams(window.location.search)
				const appType = urlParams.get('appType')
				const platform = urlParams.get('platform')

				// 检查是否存在APP注入的全局对象
				const hasAppObject = window.YuanApp || window.webkit?.messageHandlers?.YuanApp

				return appType === 'app' || hasAppObject
				// #endif

				// #ifndef H5
				return false
				// #endif
			},

			/**
			 * 使用原生App扫码功能
			 */
			async scanWithNativeApp() {
				try {
					console.log('[扫码] 调用原生App扫码功能')

					// 显示加载提示
					uni.showLoading({
						title: '正在启动扫码...',
						mask: true
					})

					// 调用App扫码服务
					const scanService = AppServiceAdapter.getService('scan')
					if (!scanService) {
						throw new Error('扫码服务不可用')
					}

					const result = await scanService.scanCode({
						scanType: 'all', // 支持二维码和条形码
						onlyFromCamera: false, // 允许从相册选择
						prompt: '请扫描设备背面或包装盒上的二维码/条形码'
					})

					uni.hideLoading()

					console.log('[扫码] 原生扫码结果:', result)

					// 处理扫码结果
					if (result && result.success && result.data && result.data.result) {
						this.handleScanResult({
							scanType: result.data.scanType || 'QR_CODE',
							result: result.data.result
						})
					} else {
						throw new Error(result?.error || '扫码失败')
					}

				} catch (error) {
					uni.hideLoading()
					console.error('[扫码] 原生扫码失败:', error)

					// 如果原生扫码失败，回退到uni.scanCode
					uni.showModal({
						title: '扫码失败',
						content: '原生扫码功能不可用，是否使用备用扫码方式？',
						success: (res) => {
							if (res.confirm) {
								this.scanWithUniApp()
							}
						}
					})
				}
			},

			/**
			 * 使用uni.scanCode扫码功能
			 */
			scanWithUniApp() {
				console.log('[扫码] 使用uni.scanCode扫码')

				// 允许从相机和相册扫码
				uni.scanCode({
					success: (res) => {
						console.log('[扫码] uni.scanCode成功:', res)
						this.handleScanResult(res)
					},
					fail: (err) => {
						console.error('[扫码] uni.scanCode失败:', err)
						uni.showToast({
							duration: 2000,
							title: '二维码无法识别',
							icon: 'none'
						})
					}
				})
			},

			/**
			 * 处理扫码结果的通用方法
			 * @param {Object} res 扫码结果
			 * @param {string} res.scanType 扫码类型
			 * @param {string} res.result 扫码内容
			 */
			handleScanResult(res) {
				console.log('[扫码] 处理扫码结果 - 条码类型：' + res.scanType)
				console.log('[扫码] 处理扫码结果 - 条码内容：' + res.result)

				if (!res.result) {
					uni.showToast({
						title: '解析二维码错误',
						icon: 'none',
						duration: 2000
					})
					return
				}

				let _devCode
				const isurl = isUrl(res.result + "")

				if (isurl) {
					console.log('[扫码] 二维码为URL格式')
					_devCode = getUrlParam(res.result, "id")
					if (!_devCode) {
						_devCode = getUrlParam(res.result, "mdid")
					}
					if (!_devCode) {
						_devCode = getUrlParam(res.result, "devCode")
					}
				} else {
					console.log('[扫码] 二维码为设备ID格式')
					_devCode = res.result
				}

				console.log('[扫码] 解析出的设备编码：' + _devCode)

				if (!_devCode) {
					uni.showToast({
						title: '无法识别设备编码',
						icon: 'none',
						duration: 2000
					})
					return
				}

				// 保存设备编码并跳转
				uni.setStorageSync('devCode', _devCode)
				uni.removeStorageSync('devId')

				const url = `/pagesDevice/add/result?devCode=${_devCode}`
				console.log('[扫码] 跳转到结果页面:', url)

				uni.redirectTo({
					url
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	// #ifdef H5
	height: 100%;
	// #endif
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");

.device-index-page {
	height: 100vh;
	// #ifdef H5
	height: 100%;
	// #endif
	position: relative;
	overflow: hidden;
	box-sizing: border-box;
	text-align: center;

	.img-center-bg {
		width: 690rpx;
		height: 450rpx;
		margin: 0 auto;
	}

	.tip {
		display: block;
		margin-top: 30rpx;
		margin-left: 28rpx;
		font-size: 30rpx;
		color: #000;
		font-size: 30rpx;
		font-weight: 500;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.scan-code-btn {
			display: block;
			margin-top: 37rpx;
		}
	}

}
</style>
